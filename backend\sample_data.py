#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hayvan Yönet<PERSON> - Sahte Veri Oluşturucu
Bu script veritabanına gerçekçi sahte veriler ekler.
SQLite tabanlı main.py ile uyumlu hale getirildi.
"""

import sqlite3
import uuid
import random
from datetime import datetime, timedelta

def get_db_connection():
    """SQLite veritabanı bağlantısı"""
    conn = sqlite3.connect('hayvancilik.db')
    conn.row_factory = sqlite3.Row
    return conn

def clear_existing_data():
    """Mevcut verileri temizle"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Tabloları temizle (foreign key sırasına dikkat et)
    tables = [
        'ration_components', 'rations', 'simulation_details', 'simulations',
        'animal_vaccinations', 'health_records', 'breeding_records',
        'calving_records', 'reminders', 'animals', 'feeds', 'farms'
    ]

    for table in tables:
        cursor.execute(f"DELETE FROM {table}")

    conn.commit()
    conn.close()
    print("🗑️ Mevcut veriler temizlendi")

def create_farm():
    """Çiftlik oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    farm_id = str(uuid.uuid4())

    cursor.execute('''
        INSERT INTO farms (
            id, name, location, established_date,
            total_land_hectares, pasture_land_hectares, barn_capacity,
            feed_storage_capacity_tons, silage_capacity_tons, hay_storage_capacity_tons,
            water_storage_capacity_liters, milking_parlor_capacity,
            quarantine_facility_capacity, hospital_pen_capacity,
            handling_facility_present, scale_capacity_kg
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        farm_id,
        'Teslime Hanım Çiftliği',
        'Bozdoğan, Aydın',
        '2020-03-15',
        150.0,  # total_land_hectares
        80.0,   # pasture_land_hectares
        200,    # barn_capacity
        500.0,  # feed_storage_capacity_tons
        1000.0, # silage_capacity_tons
        300.0,  # hay_storage_capacity_tons
        50000.0, # water_storage_capacity_liters
        50,     # milking_parlor_capacity
        10,     # quarantine_facility_capacity
        5,      # hospital_pen_capacity
        True,   # handling_facility_present
        1000.0  # scale_capacity_kg
    ))

    conn.commit()
    conn.close()

    print(f"✅ Çiftlik oluşturuldu: Teslime Hanım Çiftliği (ID: {farm_id})")
    return farm_id

def create_feeds(farm_id):
    """Gerçek yem verilerini oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Gerçek Türkiye yem verileri
    feeds_data = [
        # Konsantre Yemler
        {
            'name': 'Süt İneği Konsantresi (18% HP)',
            'feed_type': 'concentrate',
            'cost_per_kg': 8.50,
            'dry_matter_percentage': 88.0,
            'crude_protein_percentage': 18.0,
            'metabolizable_energy_mcal_kg': 2.85,
            'storage_life_days': 180,
            'moisture_content_percentage': 12.0
        },
        {
            'name': 'Besi Sığırı Konsantresi (16% HP)',
            'feed_type': 'concentrate',
            'cost_per_kg': 7.80,
            'dry_matter_percentage': 87.0,
            'crude_protein_percentage': 16.0,
            'metabolizable_energy_mcal_kg': 2.90,
            'storage_life_days': 180,
            'moisture_content_percentage': 13.0
        },
        {
            'name': 'Buzağı Başlangıç Yemi (20% HP)',
            'feed_type': 'concentrate',
            'cost_per_kg': 9.20,
            'dry_matter_percentage': 89.0,
            'crude_protein_percentage': 20.0,
            'metabolizable_energy_mcal_kg': 3.10,
            'storage_life_days': 120,
            'moisture_content_percentage': 11.0
        },
        {
            'name': 'Arpa',
            'feed_type': 'grain',
            'cost_per_kg': 4.20,
            'dry_matter_percentage': 87.0,
            'crude_protein_percentage': 11.5,
            'metabolizable_energy_mcal_kg': 2.95,
            'storage_life_days': 365,
            'moisture_content_percentage': 13.0
        },
        {
            'name': 'Mısır',
            'feed_type': 'grain',
            'cost_per_kg': 4.80,
            'dry_matter_percentage': 86.0,
            'crude_protein_percentage': 8.5,
            'metabolizable_energy_mcal_kg': 3.15,
            'storage_life_days': 365,
            'moisture_content_percentage': 14.0
        },
        {
            'name': 'Buğday Kepeği',
            'feed_type': 'byproduct',
            'cost_per_kg': 3.50,
            'dry_matter_percentage': 88.0,
            'crude_protein_percentage': 15.5,
            'metabolizable_energy_mcal_kg': 2.20,
            'storage_life_days': 90,
            'moisture_content_percentage': 12.0
        },
        {
            'name': 'Ayçiçeği Küspesi',
            'feed_type': 'byproduct',
            'cost_per_kg': 6.20,
            'dry_matter_percentage': 90.0,
            'crude_protein_percentage': 28.0,
            'metabolizable_energy_mcal_kg': 2.40,
            'storage_life_days': 120,
            'moisture_content_percentage': 10.0
        },
        {
            'name': 'Soya Küspesi',
            'feed_type': 'byproduct',
            'cost_per_kg': 12.50,
            'dry_matter_percentage': 89.0,
            'crude_protein_percentage': 44.0,
            'metabolizable_energy_mcal_kg': 2.65,
            'storage_life_days': 180,
            'moisture_content_percentage': 11.0
        },
        # Kaba Yemler
        {
            'name': 'Yonca Kuru Otu (1. Kalite)',
            'feed_type': 'hay',
            'cost_per_kg': 2.80,
            'dry_matter_percentage': 88.0,
            'crude_protein_percentage': 18.0,
            'metabolizable_energy_mcal_kg': 2.20,
            'storage_life_days': 730,
            'moisture_content_percentage': 12.0
        },
        {
            'name': 'Yonca Kuru Otu (2. Kalite)',
            'feed_type': 'hay',
            'cost_per_kg': 2.20,
            'dry_matter_percentage': 86.0,
            'crude_protein_percentage': 15.0,
            'metabolizable_energy_mcal_kg': 2.00,
            'storage_life_days': 730,
            'moisture_content_percentage': 14.0
        },
        {
            'name': 'Çayır Otu Kuru Otu',
            'feed_type': 'hay',
            'cost_per_kg': 1.80,
            'dry_matter_percentage': 85.0,
            'crude_protein_percentage': 8.0,
            'metabolizable_energy_mcal_kg': 1.90,
            'storage_life_days': 730,
            'moisture_content_percentage': 15.0
        },
        {
            'name': 'Buğday Samanı',
            'feed_type': 'straw',
            'cost_per_kg': 0.80,
            'dry_matter_percentage': 90.0,
            'crude_protein_percentage': 3.5,
            'metabolizable_energy_mcal_kg': 1.20,
            'storage_life_days': 365,
            'moisture_content_percentage': 10.0
        },
        # Silajlar
        {
            'name': 'Mısır Silajı (Kaliteli)',
            'feed_type': 'silage',
            'cost_per_kg': 1.20,
            'dry_matter_percentage': 35.0,
            'crude_protein_percentage': 8.0,
            'metabolizable_energy_mcal_kg': 2.40,
            'storage_life_days': 365,
            'moisture_content_percentage': 65.0
        },
        {
            'name': 'Yonca Silajı',
            'feed_type': 'silage',
            'cost_per_kg': 1.50,
            'dry_matter_percentage': 40.0,
            'crude_protein_percentage': 15.0,
            'metabolizable_energy_mcal_kg': 2.10,
            'storage_life_days': 365,
            'moisture_content_percentage': 60.0
        },
        {
            'name': 'Sorgum Silajı',
            'feed_type': 'silage',
            'cost_per_kg': 1.00,
            'dry_matter_percentage': 30.0,
            'crude_protein_percentage': 6.5,
            'metabolizable_energy_mcal_kg': 2.20,
            'storage_life_days': 365,
            'moisture_content_percentage': 70.0
        }
    ]

    feed_ids = []
    for feed_data in feeds_data:
        feed_id = str(uuid.uuid4())
        feed_ids.append(feed_id)

        cursor.execute('''
            INSERT INTO feeds (
                id, farm_id, name, feed_type, cost_per_kg,
                dry_matter_percentage, crude_protein_percentage,
                metabolizable_energy_mcal_kg, storage_life_days,
                moisture_content_percentage
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            feed_id,
            farm_id,
            feed_data['name'],
            feed_data['feed_type'],
            feed_data['cost_per_kg'],
            feed_data['dry_matter_percentage'],
            feed_data['crude_protein_percentage'],
            feed_data['metabolizable_energy_mcal_kg'],
            feed_data['storage_life_days'],
            feed_data['moisture_content_percentage']
        ))

    conn.commit()
    conn.close()

    print(f"✅ {len(feeds_data)} gerçek yem verisi oluşturuldu")
    return feed_ids

def create_animals(farm_id):
    """20 hayvan oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Irk dağılımı
    breeds = ['holstein', 'angus', 'simmental']
    breed_weights = [0.5, 0.3, 0.2]  # Holstein ağırlıklı

    animal_ids = []

    for i in range(20):
        # Irk seçimi (ağırlıklı)
        breed = random.choices(breeds, weights=breed_weights)[0]

        # Cinsiyet (60% dişi, 40% erkek)
        gender = 'female' if random.random() < 0.6 else 'male'

        # Yaş (6-48 ay arası)
        age_months = random.randint(6, 48)
        birth_date = (datetime.now() - timedelta(days=age_months * 30.44)).date()

        # Irk özelliklerine göre ağırlık hesaplama
        if breed == 'holstein':
            if gender == 'female':
                mature_weight = 650
                birth_weight = 40
            else:
                mature_weight = 900
                birth_weight = 45
        elif breed == 'angus':
            if gender == 'female':
                mature_weight = 550
                birth_weight = 35
            else:
                mature_weight = 850
                birth_weight = 38
        else:  # simmental
            if gender == 'female':
                mature_weight = 750
                birth_weight = 45
            else:
                mature_weight = 1100
                birth_weight = 50

        # Yaşa göre ağırlık hesaplama (sigmoid büyüme eğrisi)
        age_factor = min(age_months / 30.0, 1.0)  # 30 ayda olgunluk
        sigmoid_factor = 1 / (1 + 2.71828 ** (-5 * (age_factor - 0.5)))
        current_weight = birth_weight + (mature_weight - birth_weight) * sigmoid_factor

        # Rastgele varyasyon (%10)
        current_weight *= random.uniform(0.9, 1.1)
        current_weight = max(current_weight, birth_weight)

        # Durum belirleme
        if age_months < 8:
            status = 'calf'
        elif age_months < 15:
            status = 'heifer' if gender == 'female' else 'young_bull'
        elif gender == 'female':
            if breed == 'holstein':
                status = random.choice(['milking', 'dry', 'breeding'])
            else:
                status = random.choice(['breeding', 'pregnant'])
        else:
            status = random.choice(['breeding', 'fattening'])

        # Gebelik durumu (sadece 18+ ay dişiler için)
        is_pregnant = False
        pregnancy_start_date = None
        expected_calving_date = None

        if (gender == 'female' and age_months >= 18 and
            status in ['breeding', 'pregnant'] and random.random() < 0.4):
            is_pregnant = True
            pregnancy_days = random.randint(60, 270)  # Gebelik süresi
            pregnancy_start_date = (datetime.now() - timedelta(days=pregnancy_days)).date()
            expected_calving_date = (pregnancy_start_date + timedelta(days=283)).isoformat()
            pregnancy_start_date = pregnancy_start_date.isoformat()

        # Vücut kondisyon skoru
        if status == 'calf':
            bcs = round(random.uniform(2.0, 3.5), 1)
        elif status == 'milking':
            bcs = round(random.uniform(2.5, 3.5), 1)
        else:
            bcs = round(random.uniform(3.0, 4.0), 1)

        # Satın alma bilgileri (70% satın alınmış)
        purchase_price = None
        purchase_date = None
        if random.random() < 0.7:
            if status == 'calf':
                purchase_price = random.uniform(3000, 6000)
            elif gender == 'female':
                purchase_price = random.uniform(12000, 25000)
            else:
                purchase_price = random.uniform(15000, 35000)

            purchase_date = (datetime.now() - timedelta(days=random.randint(30, 365*2))).date().isoformat()

        # Kulak küpe numarası
        tag = f"TR{farm_id[:8].upper()}{i+1:03d}"

        animal_id = str(uuid.uuid4())
        animal_ids.append(animal_id)

        cursor.execute('''
            INSERT INTO animals (
                id, farm_id, tag, breed, birth_date, gender,
                current_weight_kg, body_condition_score, status,
                is_pregnant, pregnancy_start_date, expected_calving_date,
                purchase_price, purchase_date, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            animal_id,
            farm_id,
            tag,
            breed,
            birth_date.isoformat(),
            gender,
            round(current_weight, 1),
            bcs,
            status,
            is_pregnant,
            pregnancy_start_date,
            expected_calving_date,
            purchase_price,
            purchase_date,
            f"{breed.title()} ırkı {gender} hayvan. Yaş: {age_months} ay."
        ))

    conn.commit()
    conn.close()

    print(f"✅ {len(animal_ids)} hayvan oluşturuldu")
    return animal_ids

def main():
    """Ana fonksiyon"""
    print("🚀 Sahte veri oluşturma başlıyor...")
    print("📋 SQLite tabanlı sistem için optimize edildi")

    try:
        # Mevcut verileri temizle
        clear_existing_data()

        # Çiftlik oluştur
        farm_id = create_farm()

        # Yemler oluştur
        feed_ids = create_feeds(farm_id)

        # Hayvanlar oluştur
        animal_ids = create_animals(farm_id)

        print("\n✅ Sahte veri oluşturma tamamlandı!")
        print(f"📊 1 çiftlik, {len(feed_ids)} yem türü, {len(animal_ids)} hayvan oluşturuldu")
        print(f"🏠 Çiftlik ID: {farm_id}")
        print("🎯 Sistem kullanıma hazır!")

    except Exception as e:
        print(f"❌ Hata: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
